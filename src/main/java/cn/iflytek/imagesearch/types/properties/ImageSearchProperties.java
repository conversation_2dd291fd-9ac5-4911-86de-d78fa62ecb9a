package cn.iflytek.imagesearch.types.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 图片搜索配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "image-search")
public class ImageSearchProperties {
    
    /**
     * 默认配置
     */
    private DefaultConfig defaultConfig = new DefaultConfig();
    
    /**
     * 语义搜索配置
     */
    private SemanticConfig semantic = new SemanticConfig();
    
    /**
     * 筛选搜索配置
     */
    private FilterConfig filter = new FilterConfig();
    
    /**
     * 图片详情配置
     */
    private DetailConfig detail = new DetailConfig();
    
    @Data
    public static class DefaultConfig {
        private Integer limit = 10;
        private Integer timeout = 5000;
    }
    
    @Data
    public static class SemanticConfig {
        private Boolean enabled = true;
        private String model = "text-embedding-ada-002";
        private Double similarityThreshold = 0.7;
    }
    
    @Data
    public static class FilterConfig {
        private Boolean enabled = true;
        private Integer maxFilters = 10;
    }
    
    @Data
    public static class DetailConfig {
        private Boolean includeExif = true;
        private Boolean includeSimilar = true;
        private Integer similarLimit = 5;
    }
}
