package cn.iflytek.imagesearch.domain.model.request;

import lombok.Data;

/**
 * 获取图片详情请求
 */
@Data
public class ImageDetailRequest {
    
    /**
     * 图片ID
     */
    private String imageId;
    
    /**
     * 图片URL（可选，如果没有imageId可以通过URL获取）
     */
    private String imageUrl;
    
    /**
     * 是否包含扩展信息（EXIF数据、颜色分析等）
     */
    private Boolean includeExtendedInfo = false;
    
    /**
     * 是否包含相似图片推荐
     */
    private Boolean includeSimilarImages = false;
    
    /**
     * 相似图片推荐数量
     */
    private Integer similarImagesLimit = 5;
}
