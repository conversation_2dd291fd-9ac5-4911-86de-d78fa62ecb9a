package cn.iflytek.imagesearch.domain.model.request;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 条件筛选搜索图片请求
 */
@Data
public class ImageFilterRequest {
    
    /**
     * 图片格式：jpg, png, gif, webp, svg等
     */
    private List<String> formats;
    
    /**
     * 图片尺寸范围
     */
    private SizeRange sizeRange;
    
    /**
     * 文件大小范围（字节）
     */
    private FileSizeRange fileSizeRange;
    
    /**
     * 颜色主题：red, blue, green, yellow, black, white, colorful, monochrome等
     */
    private List<String> colors;
    
    /**
     * 图片类型：photo(照片), illustration(插图), vector(矢量图), clipart(剪贴画)
     */
    private List<String> imageTypes;
    
    /**
     * 图片方向：horizontal(横向), vertical(纵向), square(正方形)
     */
    private String orientation;
    
    /**
     * 标签/分类
     */
    private List<String> tags;
    
    /**
     * 创建时间范围
     */
    private DateRange dateRange;
    
    /**
     * 作者/来源
     */
    private String author;
    
    /**
     * 许可证类型：free, commercial, creative_commons等
     */
    private List<String> licenses;
    
    /**
     * 返回结果数量限制
     */
    private Integer limit = 10;
    
    /**
     * 偏移量（分页）
     */
    private Integer offset = 0;
    
    /**
     * 排序方式
     */
    private String sortBy = "relevance";
    
    /**
     * 排序顺序
     */
    private String sortOrder = "desc";
    
    @Data
    public static class SizeRange {
        private Integer minWidth;
        private Integer maxWidth;
        private Integer minHeight;
        private Integer maxHeight;
    }
    
    @Data
    public static class FileSizeRange {
        private Long minSize;
        private Long maxSize;
    }
    
    @Data
    public static class DateRange {
        private LocalDateTime startDate;
        private LocalDateTime endDate;
    }
}
