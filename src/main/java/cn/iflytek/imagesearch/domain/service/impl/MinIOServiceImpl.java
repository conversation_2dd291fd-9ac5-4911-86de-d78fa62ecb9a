package cn.iflytek.imagesearch.domain.service.impl;

import cn.iflytek.imagesearch.domain.service.IMinIOService;
import cn.iflytek.imagesearch.types.properties.MinIOProperties;
import io.minio.*;
import io.minio.http.Method;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * MinIO对象存储服务实现
 */
@Service
public class MinIOServiceImpl implements IMinIOService {
    
    private final Logger log = LoggerFactory.getLogger(MinIOServiceImpl.class);
    
    private final MinioClient minioClient;
    private final MinIOProperties minIOProperties;
    
    public MinIOServiceImpl(MinioClient minioClient, MinIOProperties minIOProperties) {
        this.minioClient = minioClient;
        this.minIOProperties = minIOProperties;
        initializeBucket();
    }
    
    /**
     * 初始化存储桶
     */
    private void initializeBucket() {
        try {
            String bucketName = minIOProperties.getBucketName();
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build());
            
            if (!exists) {
                minioClient.makeBucket(MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .build());
                log.info("创建MinIO存储桶: {}", bucketName);
            }
            
            // 设置公开读取策略（如果配置了）
            if (minIOProperties.getPublicRead()) {
                String policy = "{\n" +
                        "  \"Version\": \"2012-10-17\",\n" +
                        "  \"Statement\": [\n" +
                        "    {\n" +
                        "      \"Effect\": \"Allow\",\n" +
                        "      \"Principal\": \"*\",\n" +
                        "      \"Action\": \"s3:GetObject\",\n" +
                        "      \"Resource\": \"arn:aws:s3:::" + bucketName + "/*\"\n" +
                        "    }\n" +
                        "  ]\n" +
                        "}";
                
                minioClient.setBucketPolicy(SetBucketPolicyArgs.builder()
                        .bucket(bucketName)
                        .config(policy)
                        .build());
            }
            
        } catch (Exception e) {
            log.error("初始化MinIO存储桶失败", e);
        }
    }
    
    @Override
    public String uploadImage(MultipartFile file, String fileName) {
        try {
            return uploadImage(file.getInputStream(), fileName, file.getContentType(), file.getSize());
        } catch (Exception e) {
            log.error("上传图片文件失败: {}", fileName, e);
            throw new RuntimeException("上传图片文件失败", e);
        }
    }
    
    @Override
    public String uploadImage(InputStream inputStream, String fileName, String contentType, long size) {
        try {
            String objectName = minIOProperties.getImagePathPrefix() + generateUniqueFileName(fileName);
            
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minIOProperties.getBucketName())
                    .object(objectName)
                    .stream(inputStream, size, -1)
                    .contentType(contentType)
                    .build());
            
            log.info("图片上传成功: {}", objectName);
            return objectName;
            
        } catch (Exception e) {
            log.error("上传图片流失败: {}", fileName, e);
            throw new RuntimeException("上传图片流失败", e);
        }
    }
    
    @Override
    public String uploadThumbnail(InputStream inputStream, String fileName, String contentType, long size) {
        try {
            String objectName = minIOProperties.getThumbnailPathPrefix() + generateUniqueFileName(fileName);
            
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minIOProperties.getBucketName())
                    .object(objectName)
                    .stream(inputStream, size, -1)
                    .contentType(contentType)
                    .build());
            
            log.info("缩略图上传成功: {}", objectName);
            return objectName;
            
        } catch (Exception e) {
            log.error("上传缩略图失败: {}", fileName, e);
            throw new RuntimeException("上传缩略图失败", e);
        }
    }
    
    @Override
    public String getImageUrl(String filePath) {
        if (minIOProperties.getPublicRead()) {
            return minIOProperties.getEndpoint() + "/" + minIOProperties.getBucketName() + "/" + filePath;
        } else {
            return getPresignedUrl(filePath, 3600); // 1小时有效期
        }
    }
    
    @Override
    public String getPresignedUrl(String filePath, int expiry) {
        try {
            return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minIOProperties.getBucketName())
                    .object(filePath)
                    .expiry(expiry, TimeUnit.SECONDS)
                    .build());
        } catch (Exception e) {
            log.error("生成预签名URL失败: {}", filePath, e);
            throw new RuntimeException("生成预签名URL失败", e);
        }
    }
    
    @Override
    public boolean deleteImage(String filePath) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(minIOProperties.getBucketName())
                    .object(filePath)
                    .build());
            
            log.info("删除图片成功: {}", filePath);
            return true;
            
        } catch (Exception e) {
            log.error("删除图片失败: {}", filePath, e);
            return false;
        }
    }
    
    @Override
    public boolean fileExists(String filePath) {
        try {
            minioClient.statObject(StatObjectArgs.builder()
                    .bucket(minIOProperties.getBucketName())
                    .object(filePath)
                    .build());
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public FileInfo getFileInfo(String filePath) {
        try {
            StatObjectResponse stat = minioClient.statObject(StatObjectArgs.builder()
                    .bucket(minIOProperties.getBucketName())
                    .object(filePath)
                    .build());
            
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(filePath);
            fileInfo.setSize(stat.size());
            fileInfo.setContentType(stat.contentType());
            fileInfo.setEtag(stat.etag());
            
            return fileInfo;
            
        } catch (Exception e) {
            log.error("获取文件信息失败: {}", filePath, e);
            throw new RuntimeException("获取文件信息失败", e);
        }
    }
    
    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFileName) {
        String extension = "";
        int lastDotIndex = originalFileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = originalFileName.substring(lastDotIndex);
        }
        return UUID.randomUUID().toString() + extension;
    }
}
