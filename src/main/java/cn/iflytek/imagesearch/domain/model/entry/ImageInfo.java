package cn.iflytek.imagesearch.domain.model.entry;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 图片信息
 */
@Data
public class ImageInfo {
    
    /**
     * 图片ID
     */
    private String id;
    
    /**
     * 图片标题
     */
    private String title;
    
    /**
     * 图片描述
     */
    private String description;
    
    /**
     * 图片URL
     */
    private String url;
    
    /**
     * 缩略图URL
     */
    private String thumbnailUrl;
    
    /**
     * 图片格式
     */
    private String format;
    
    /**
     * 图片宽度
     */
    private Integer width;
    
    /**
     * 图片高度
     */
    private Integer height;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 主要颜色
     */
    private List<String> dominantColors;
    
    /**
     * 标签
     */
    private List<String> tags;
    
    /**
     * 分类
     */
    private String category;
    
    /**
     * 作者/来源
     */
    private String author;
    
    /**
     * 许可证信息
     */
    private String license;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 相关性评分（搜索时使用）
     */
    private Double relevanceScore;
    
    /**
     * 扩展信息（EXIF数据等）
     */
    private Map<String, Object> extendedInfo;
    
    /**
     * 相似图片列表
     */
    private List<ImageInfo> similarImages;
}
