package cn.iflytek.imagesearch.domain.service;


import cn.iflytek.imagesearch.domain.model.request.ImageDetailRequest;
import cn.iflytek.imagesearch.domain.model.request.ImageFilterRequest;
import cn.iflytek.imagesearch.domain.model.request.ImageSearchRequest;
import cn.iflytek.imagesearch.domain.model.response.ImageDetailResponse;
import cn.iflytek.imagesearch.domain.model.response.ImageSearchResponse;

/**
 * 图片搜索服务接口
 */
public interface IImageSearchService {
    
    /**
     * 语义搜索图片
     * 基于自然语言描述进行智能图片搜索
     * 
     * @param request 搜索请求
     * @return 搜索结果
     */
    ImageSearchResponse semanticSearch(ImageSearchRequest request);
    
    /**
     * 条件筛选搜索图片
     * 基于具体条件和属性进行精确筛选
     * 
     * @param request 筛选请求
     * @return 搜索结果
     */
    ImageSearchResponse filterSearch(ImageFilterRequest request);
    
    /**
     * 获取图片详情
     * 获取单张图片的完整元数据信息
     *
     * @param request 详情请求
     * @return 图片详情
     */
    ImageDetailResponse getImageDetail(ImageDetailRequest request);


}
