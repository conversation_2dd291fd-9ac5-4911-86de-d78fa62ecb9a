package cn.iflytek.imagesearch;

import io.minio.BucketExistsArgs;
import io.minio.ListBucketsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.http.Method;
import io.minio.messages.Bucket;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * MinIO快速连接测试
 * 可以独立运行，不依赖Spring Boot
 */
public class MinIOQuickTest {

    private static final String ENDPOINT = "http://localhost:9000";
    private static final String ACCESS_KEY = "minioadmin";
    private static final String SECRET_KEY = "minioadmin123";
    private static final String BUCKET_NAME = "image-storage";

    public static void main(String[] args) {
        System.out.println("=== MinIO连接测试开始 ===");
        
        try {
            // 创建MinIO客户端
            MinioClient minioClient = MinioClient.builder()
                    .endpoint(ENDPOINT)
                    .credentials(ACCESS_KEY, SECRET_KEY)
                    .build();
            
            System.out.println("✅ MinIO客户端创建成功");
            
            // 测试连接 - 列出所有存储桶
            testConnection(minioClient);
            
            // 测试存储桶操作
            testBucketOperations(minioClient);
            
            // 测试文件上传
            testFileUpload(minioClient);
            
            System.out.println("=== 所有测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试连接
     */
    private static void testConnection(MinioClient minioClient) {
        try {
            System.out.println("\n--- 测试连接 ---");
            List<Bucket> buckets = minioClient.listBuckets();
            System.out.println("✅ 连接成功！当前存储桶数量: " + buckets.size());
            
            for (Bucket bucket : buckets) {
                System.out.println("  - 存储桶: " + bucket.name() + " (创建时间: " + bucket.creationDate() + ")");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 连接测试失败: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试存储桶操作
     */
    private static void testBucketOperations(MinioClient minioClient) {
        try {
            System.out.println("\n--- 测试存储桶操作 ---");
            
            // 检查存储桶是否存在
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(BUCKET_NAME)
                    .build());
            
            if (exists) {
                System.out.println("✅ 存储桶 '" + BUCKET_NAME + "' 已存在");
            } else {
                System.out.println("⚠️ 存储桶 '" + BUCKET_NAME + "' 不存在，正在创建...");
                
                // 创建存储桶
                minioClient.makeBucket(MakeBucketArgs.builder()
                        .bucket(BUCKET_NAME)
                        .build());
                
                System.out.println("✅ 存储桶 '" + BUCKET_NAME + "' 创建成功");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 存储桶操作失败: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试文件上传
     */
    private static void testFileUpload(MinioClient minioClient) {
        try {
            System.out.println("\n--- 测试文件上传 ---");
            
            // 创建测试数据
            String testContent = "这是一个MinIO连接测试文件 - " + System.currentTimeMillis();
            byte[] testData = testContent.getBytes("UTF-8");
            String objectName = "test/connection-test.txt";
            
            // 上传文件
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(BUCKET_NAME)
                    .object(objectName)
                    .stream(new ByteArrayInputStream(testData), testData.length, -1)
                    .contentType("text/plain")
                    .build());
            
            System.out.println("✅ 文件上传成功: " + objectName);
            
            // 生成访问URL
            String publicUrl = ENDPOINT + "/" + BUCKET_NAME + "/" + objectName;
            System.out.println("📄 公开访问URL: " + publicUrl);
            
            // 生成预签名URL
            String presignedUrl = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(BUCKET_NAME)
                    .object(objectName)
                    .expiry(1, TimeUnit.HOURS)
                    .build());
            
            System.out.println("🔗 预签名URL: " + presignedUrl);
            
            // 测试图片上传
            testImageUpload(minioClient);
            
        } catch (Exception e) {
            System.err.println("❌ 文件上传测试失败: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试图片上传
     */
    private static void testImageUpload(MinioClient minioClient) {
        try {
            System.out.println("\n--- 测试图片上传 ---");
            
            // 创建简单的PNG图片数据
            byte[] pngData = createSimplePngData();
            String imageName = "images/test-image-" + System.currentTimeMillis() + ".png";
            
            // 上传图片
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(BUCKET_NAME)
                    .object(imageName)
                    .stream(new ByteArrayInputStream(pngData), pngData.length, -1)
                    .contentType("image/png")
                    .build());
            
            System.out.println("✅ 图片上传成功: " + imageName);
            
            // 生成图片访问URL
            String imageUrl = ENDPOINT + "/" + BUCKET_NAME + "/" + imageName;
            System.out.println("🖼️ 图片访问URL: " + imageUrl);
            
            // 生成预签名图片URL
            String presignedImageUrl = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(BUCKET_NAME)
                    .object(imageName)
                    .expiry(24, TimeUnit.HOURS)
                    .build());
            
            System.out.println("🔗 图片预签名URL: " + presignedImageUrl);
            
            // 测试缩略图上传
            String thumbnailName = "thumbnails/thumb-" + System.currentTimeMillis() + ".png";
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(BUCKET_NAME)
                    .object(thumbnailName)
                    .stream(new ByteArrayInputStream(pngData), pngData.length, -1)
                    .contentType("image/png")
                    .build());
            
            System.out.println("✅ 缩略图上传成功: " + thumbnailName);
            String thumbnailUrl = ENDPOINT + "/" + BUCKET_NAME + "/" + thumbnailName;
            System.out.println("🖼️ 缩略图访问URL: " + thumbnailUrl);
            
        } catch (Exception e) {
            System.err.println("❌ 图片上传测试失败: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建简单的PNG图片数据
     */
    private static byte[] createSimplePngData() {
        // 1x1像素的透明PNG图片
        return new byte[]{
            (byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
            0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
            0x49, 0x48, 0x44, 0x52, // IHDR
            0x00, 0x00, 0x00, 0x01, // width: 1
            0x00, 0x00, 0x00, 0x01, // height: 1
            0x08, 0x06, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
            0x1F, 0x15, (byte) 0xC4, (byte) 0x89, // CRC
            0x00, 0x00, 0x00, 0x0B, // IDAT chunk length
            0x49, 0x44, 0x41, 0x54, // IDAT
            0x78, (byte) 0xDA, 0x62, 0x00, 0x02, 0x00, 0x00, 0x05, 0x00, 0x01, (byte) 0xE2, // compressed data
            0x26, 0x05, 0x9B, // CRC
            0x00, 0x00, 0x00, 0x00, // IEND chunk length
            0x49, 0x45, 0x4E, 0x44, // IEND
            (byte) 0xAE, 0x42, 0x60, (byte) 0x82 // CRC
        };
    }
}
