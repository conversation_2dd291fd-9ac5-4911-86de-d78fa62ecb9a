package cn.iflytek.imagesearch.domain.model.response;

import cn.iflytek.imagesearch.domain.model.entry.ImageInfo;
import lombok.Data;

import java.util.List;

/**
 * 图片搜索响应
 */
@Data
public class ImageSearchResponse {
    
    /**
     * 搜索结果列表
     */
    private List<ImageInfo> images;
    
    /**
     * 总数量
     */
    private Long totalCount;
    
    /**
     * 当前页偏移量
     */
    private Integer offset;
    
    /**
     * 当前页数量
     */
    private Integer limit;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 搜索耗时（毫秒）
     */
    private Long searchTimeMs;
    
    /**
     * 搜索建议（当搜索结果较少时）
     */
    private List<String> suggestions;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 是否成功
     */
    private Boolean success = true;
}
