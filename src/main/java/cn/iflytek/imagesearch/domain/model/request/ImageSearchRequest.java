package cn.iflytek.imagesearch.domain.model.request;

import lombok.Data;

/**
 * 语义搜索图片请求
 */
@Data
public class ImageSearchRequest {
    
    /**
     * 搜索描述 - 自然语言描述
     */
    private String description;
    
    /**
     * 搜索关键词
     */
    private String keywords;
    
    /**
     * 返回结果数量限制
     */
    private Integer limit = 10;
    
    /**
     * 偏移量（分页）
     */
    private Integer offset = 0;
    
    /**
     * 排序方式：relevance(相关性), date(时间), popularity(热度)
     */
    private String sortBy = "relevance";
    
    /**
     * 排序顺序：asc(升序), desc(降序)
     */
    private String sortOrder = "desc";
}
