package cn.iflytek.imagesearch.domain.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * MinIO对象存储服务接口
 */
public interface IMinIOService {
    
    /**
     * 上传图片文件
     * 
     * @param file 图片文件
     * @param fileName 文件名
     * @return 存储路径
     */
    String uploadImage(MultipartFile file, String fileName);
    
    /**
     * 上传图片流
     * 
     * @param inputStream 输入流
     * @param fileName 文件名
     * @param contentType 内容类型
     * @param size 文件大小
     * @return 存储路径
     */
    String uploadImage(InputStream inputStream, String fileName, String contentType, long size);
    
    /**
     * 上传缩略图
     * 
     * @param inputStream 输入流
     * @param fileName 文件名
     * @param contentType 内容类型
     * @param size 文件大小
     * @return 存储路径
     */
    String uploadThumbnail(InputStream inputStream, String fileName, String contentType, long size);
    
    /**
     * 获取图片访问URL
     * 
     * @param filePath 文件路径
     * @return 访问URL
     */
    String getImageUrl(String filePath);
    
    /**
     * 获取图片预签名URL
     * 
     * @param filePath 文件路径
     * @param expiry 过期时间(秒)
     * @return 预签名URL
     */
    String getPresignedUrl(String filePath, int expiry);
    
    /**
     * 删除图片文件
     * 
     * @param filePath 文件路径
     * @return 是否成功
     */
    boolean deleteImage(String filePath);
    
    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    boolean fileExists(String filePath);
    
    /**
     * 获取文件信息
     * 
     * @param filePath 文件路径
     * @return 文件信息
     */
    FileInfo getFileInfo(String filePath);
    
    /**
     * 文件信息类
     */
    class FileInfo {
        private String fileName;
        private long size;
        private String contentType;
        private String etag;
        
        // getters and setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public long getSize() { return size; }
        public void setSize(long size) { this.size = size; }
        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }
        public String getEtag() { return etag; }
        public void setEtag(String etag) { this.etag = etag; }
    }
}
