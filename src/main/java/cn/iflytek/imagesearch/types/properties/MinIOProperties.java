package cn.iflytek.imagesearch.types.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * MinIO配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "minio")
public class MinIOProperties {

    /**
     * MinIO服务端点
     */
    private String endpoint = "http://8.141.11.126:9000";

    /**
     * 访问密钥
     */
    private String accessKey = "minioadmin";

    /**
     * 秘密密钥
     */
    private String secretKey = "minioadmin123";

    /**
     * 存储桶名称
     */
    private String bucketName = "image-storage";

    /**
     * 图片存储路径前缀
     */
    private String imagePathPrefix = "images/";

    /**
     * 缩略图路径前缀
     */
    private String thumbnailPathPrefix = "thumbnails/";

    /**
     * 是否公开读取
     */
    private Boolean publicRead = true;

    /**
     * 上传配置
     */
    private UploadConfig upload = new UploadConfig();

    @Data
    public static class UploadConfig {
        /**
         * 最大文件大小
         */
        private String maxFileSize = "10MB";

        /**
         * 允许的文件格式
         */
        private List<String> allowedFormats = List.of("jpg", "jpeg", "png", "gif", "webp", "svg");
    }
}
