package cn.iflytek.imagesearch;

import cn.iflytek.imagesearch.domain.service.IMinIOService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.TestPropertySource;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MinIO连接和功能测试
 */
@SpringBootTest
@TestPropertySource(properties = {
    "minio.endpoint=http://localhost:9000",
    "minio.access-key=minioadmin", 
    "minio.secret-key=minioadmin123",
    "minio.bucket-name=image-storage",
    "minio.public-read=true"
})
public class MinIOConnectionTest {

    @Autowired
    private IMinIOService minIOService;

    /**
     * 测试上传图片文件
     */
    @Test
    public void testUploadImageFile() {
        System.out.println("=== 测试上传图片文件 ===");
        
        // 创建测试图片数据 (简单的PNG格式头部)
        byte[] imageData = createTestImageData();
        
        MockMultipartFile mockFile = new MockMultipartFile(
            "test-image",
            "test-image.png",
            "image/png",
            imageData
        );
        
        try {
            String filePath = minIOService.uploadImage(mockFile, "test-image.png");
            assertNotNull(filePath, "上传后应该返回文件路径");
            assertTrue(filePath.contains("images/"), "文件路径应该包含images前缀");
            assertTrue(filePath.endsWith(".png"), "文件路径应该保持原始扩展名");
            
            System.out.println("✅ 图片上传成功，文件路径: " + filePath);
            
            // 测试文件是否存在
            boolean exists = minIOService.fileExists(filePath);
            assertTrue(exists, "上传的文件应该存在");
            System.out.println("✅ 文件存在性验证通过");
            
            // 测试获取文件信息
            IMinIOService.FileInfo fileInfo = minIOService.getFileInfo(filePath);
            assertNotNull(fileInfo, "应该能获取到文件信息");
            assertEquals("image/png", fileInfo.getContentType(), "内容类型应该正确");
            System.out.println("✅ 文件信息获取成功: " + fileInfo.getContentType() + ", 大小: " + fileInfo.getSize());
            
            // 测试获取访问URL
            String imageUrl = minIOService.getImageUrl(filePath);
            assertNotNull(imageUrl, "应该能获取到访问URL");
            System.out.println("✅ 图片访问URL: " + imageUrl);
            
            // 测试URL是否可访问
            testUrlAccessibility(imageUrl);
            
            // 清理测试文件
            boolean deleted = minIOService.deleteImage(filePath);
            assertTrue(deleted, "应该能成功删除测试文件");
            System.out.println("✅ 测试文件清理完成");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
            fail("上传图片测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试上传图片流
     */
    @Test
    public void testUploadImageStream() {
        System.out.println("=== 测试上传图片流 ===");
        
        byte[] imageData = createTestImageData();
        InputStream inputStream = new ByteArrayInputStream(imageData);
        
        try {
            String filePath = minIOService.uploadImage(
                inputStream, 
                "stream-test.jpg", 
                "image/jpeg", 
                imageData.length
            );
            
            assertNotNull(filePath, "上传后应该返回文件路径");
            assertTrue(filePath.contains("images/"), "文件路径应该包含images前缀");
            System.out.println("✅ 图片流上传成功，文件路径: " + filePath);
            
            // 获取访问URL并测试
            String imageUrl = minIOService.getImageUrl(filePath);
            System.out.println("✅ 图片访问URL: " + imageUrl);
            testUrlAccessibility(imageUrl);
            
            // 清理
            minIOService.deleteImage(filePath);
            System.out.println("✅ 测试文件清理完成");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
            fail("上传图片流测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试上传缩略图
     */
    @Test
    public void testUploadThumbnail() {
        System.out.println("=== 测试上传缩略图 ===");
        
        byte[] imageData = createTestImageData();
        InputStream inputStream = new ByteArrayInputStream(imageData);
        
        try {
            String filePath = minIOService.uploadThumbnail(
                inputStream, 
                "thumbnail-test.png", 
                "image/png", 
                imageData.length
            );
            
            assertNotNull(filePath, "上传后应该返回文件路径");
            assertTrue(filePath.contains("thumbnails/"), "文件路径应该包含thumbnails前缀");
            System.out.println("✅ 缩略图上传成功，文件路径: " + filePath);
            
            // 获取访问URL并测试
            String imageUrl = minIOService.getImageUrl(filePath);
            System.out.println("✅ 缩略图访问URL: " + imageUrl);
            testUrlAccessibility(imageUrl);
            
            // 清理
            minIOService.deleteImage(filePath);
            System.out.println("✅ 测试文件清理完成");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
            fail("上传缩略图测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试预签名URL
     */
    @Test
    public void testPresignedUrl() {
        System.out.println("=== 测试预签名URL ===");
        
        byte[] imageData = createTestImageData();
        MockMultipartFile mockFile = new MockMultipartFile(
            "presigned-test",
            "presigned-test.png",
            "image/png",
            imageData
        );
        
        try {
            // 先上传一个文件
            String filePath = minIOService.uploadImage(mockFile, "presigned-test.png");
            System.out.println("✅ 测试文件上传成功: " + filePath);
            
            // 获取预签名URL
            String presignedUrl = minIOService.getPresignedUrl(filePath, 3600);
            assertNotNull(presignedUrl, "应该能获取到预签名URL");
            assertTrue(presignedUrl.contains("localhost:9000"), "预签名URL应该包含正确的端点");
            System.out.println("✅ 预签名URL: " + presignedUrl);
            
            // 测试预签名URL是否可访问
            testUrlAccessibility(presignedUrl);
            
            // 清理
            minIOService.deleteImage(filePath);
            System.out.println("✅ 测试文件清理完成");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
            fail("预签名URL测试失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试图片数据
     */
    private byte[] createTestImageData() {
        // 创建一个简单的PNG文件头部和数据
        return new byte[]{
            (byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
            0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
            0x49, 0x48, 0x44, 0x52, // IHDR
            0x00, 0x00, 0x00, 0x01, // width: 1
            0x00, 0x00, 0x00, 0x01, // height: 1
            0x08, 0x02, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
            (byte) 0x90, 0x77, 0x53, (byte) 0xDE, // CRC
            0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
            0x49, 0x44, 0x41, 0x54, // IDAT
            0x08, 0x1D, 0x01, 0x01, 0x00, 0x00, (byte) 0xFE, (byte) 0xFF, 0x00, 0x00, 0x00, 0x02, // compressed data
            0x00, 0x01, // CRC
            0x00, 0x00, 0x00, 0x00, // IEND chunk length
            0x49, 0x45, 0x4E, 0x44, // IEND
            (byte) 0xAE, 0x42, 0x60, (byte) 0x82 // CRC
        };
    }

    /**
     * 测试URL可访问性
     */
    private void testUrlAccessibility(String url) {
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                System.out.println("✅ URL可访问，响应码: " + responseCode);
            } else {
                System.out.println("⚠️ URL响应码: " + responseCode);
            }
            
            connection.disconnect();
            
        } catch (IOException e) {
            System.err.println("❌ URL访问测试失败: " + e.getMessage());
            // 不抛出异常，因为网络问题不应该导致整个测试失败
        }
    }
}
